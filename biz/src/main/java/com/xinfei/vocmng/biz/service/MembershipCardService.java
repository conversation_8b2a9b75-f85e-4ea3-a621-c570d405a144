/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocprod.itl.rr.MembershipCardQueryRequest;
import com.xinfei.vocprod.itl.rr.MembershipCardQueryResponse;

/**
 * 会员卡服务接口
 *
 * <AUTHOR>
 * @version $ MembershipCardService, v 0.1 2025/8/25 15:00 shaohui.chen Exp $
 */
public interface MembershipCardService {

    /**
     * 查询飞享&飞跃会员卡列表
     *
     * @param request 查询请求
     * @return 会员卡列表响应
     */
    MembershipCardQueryResponse queryMembershipCards(MembershipCardQueryRequest request);
}
