package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.req.SetComSummaryRequest;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import com.xinfei.vocmng.itl.rr.udesk.UdeskAgentIdRequest;
import com.xinfei.vocmng.itl.rr.udesk.UdeskAgentIdResponse;
import com.xinfei.vocmng.itl.rr.udesk.UdeskAuthResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "UDesk")
@RequestMapping("/external/udesk")
public interface UDeskApi {

    @ApiOperation("udesk进线通知")
    @PostMapping(value = "/call")
    ApiResponse<String> setComSummary(@RequestBody SetComSummaryRequest setComSummaryRequest);

    @ApiOperation("udesk对话记录同步")
    @GetMapping(value = "/sessionsSync")
    ApiResponse<Boolean> sessionsSync(@ApiParam(value = "开始时间") @RequestParam(value = "startTime",required = false) String startTime,@ApiParam(value = "结束时间") @RequestParam(value = "endTime",required = false) String endTime);

    @ApiOperation("udesk通话记录同步")
    @GetMapping(value = "/callSync")
    ApiResponse<Boolean> callSync(@ApiParam(value = "开始时间") @RequestParam(value = "startTime",required = false) String startTime,@ApiParam(value = "结束时间") @RequestParam(value = "endTime",required = false) String endTime);

    @ApiOperation("udesk机器人对话记录同步")
    @GetMapping(value = "/robotSync")
    ApiResponse<Boolean> robotSync(@ApiParam(value = "开始时间") @RequestParam(value = "startTime",required = false) String startTime,@ApiParam(value = "结束时间") @RequestParam(value = "endTime",required = false) String endTime);

    @ApiOperation("对公账户查询")
    @PostMapping(value = "/queryPublicAccountInfo")
    ApiResponse<PublicAccountInfo> queryPublicAccountInfo(@Validated @RequestBody PublicAccountRequest req);

    @ApiOperation("udesk鉴权")
    @GetMapping(value = "/promiseSign")
    ApiResponse<UdeskAuthResponse> promiseSign();

    @ApiOperation("查询udesk系统AgentId")
    @PostMapping(value = "/queryAgentId")
    ApiResponse<List<UdeskAgentIdResponse>> queryAgentId(@RequestBody UdeskAgentIdRequest req);

    @ApiOperation("udesk机器人明细同步")
    @GetMapping(value = "/robotDetailsSync")
    ApiResponse<Boolean> robotDetailsSync(@ApiParam(value = "开始时间") @RequestParam(value = "startTime",required = false) String startTime,@ApiParam(value = "结束时间") @RequestParam(value = "endTime",required = false) String endTime);

    @ApiOperation("udesk全量同步")
    @GetMapping(value = "/uDeskSyncAll")
    ApiResponse<Boolean> uDeskSyncAll(@ApiParam(value = "开始时间") @RequestParam(value = "startTime",required = false) String startTime,@ApiParam(value = "结束时间") @RequestParam(value = "endTime",required = false) String endTime);

}
