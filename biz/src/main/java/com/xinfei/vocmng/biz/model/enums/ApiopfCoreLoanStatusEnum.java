package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: long.cheng
 * @Date: 2025/7/28 17:40
 * @description:
 */
@Getter
public enum ApiopfCoreLoanStatusEnum {

    to_be_paid("to_be_paid", "待支付"),
    pay_success("pay_success", "支付成功"),
    pay_fail("pay_fail", "支付失败"),
    in_refund("in_refund", "退款中"),
    refund_success("refund_success", "退款成功"),
    refund_fail("refund_fail", "退款失败"),
    cancel_order("cancel_order", "取消订单"),
    ;

    private final String code;
    private final String desc;

    ApiopfCoreLoanStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ApiopfCoreLoanStatusEnum status : ApiopfCoreLoanStatusEnum.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status.getDesc();
            }
        }
        return null;
    }
}
