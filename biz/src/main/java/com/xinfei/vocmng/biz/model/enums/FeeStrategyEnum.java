package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;


/**
 * 费控场景对应的枚举项
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@Getter
public enum FeeStrategyEnum {
    ORDER_REDUCTIONS("order_reductions", "订单减免"),
    VIP_CARD_REFUNDS("vip_card_refunds", "会员卡退款"),
    DEDUCTIONS_FROM("deductions_from", "抵扣订单from"),
    DEDUCTIONS_TO("deductions_to", "抵扣订单to"),
    ORDER_REFUND("order_refund", "订单退款"),
    CARD_DISCOUNT("card_discount", "飞跃会员减免"),
    FEE_RATE_LIMIT_REDUCTIONS("fee_rate_limit_reductions", "固定费率减免"),;


    private final String scene;
    private final String comment;

    FeeStrategyEnum(String scene, String comment) {
        this.scene = scene;
        this.comment = comment;
    }
}
