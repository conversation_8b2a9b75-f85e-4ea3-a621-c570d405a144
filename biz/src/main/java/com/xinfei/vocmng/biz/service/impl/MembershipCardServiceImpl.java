/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RenewLogAdminDto;
import com.xinfei.vocmng.biz.mapstruct.VipCardConverter;
import com.xinfei.vocmng.biz.rr.dto.MemberCardDto;
import com.xinfei.vocmng.biz.service.MembershipCardService;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocprod.itl.rr.MembershipCardDto;
import com.xinfei.vocprod.itl.rr.MembershipCardQueryRequest;
import com.xinfei.vocprod.itl.rr.MembershipCardQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员卡服务实现类
 *
 * <AUTHOR>
 * @version $ MembershipCardServiceImpl, v 0.1 2025/8/25 15:00 shaohui.chen Exp $
 */
@Slf4j
@Service
public class MembershipCardServiceImpl implements MembershipCardService {

    @Autowired
    private VipFacadeClientImpl vipFacadeClient;

    @Override
    public MembershipCardQueryResponse queryMembershipCards(MembershipCardQueryRequest request) {
        if (request == null || StringUtils.isBlank(request.getUserNo())) {
            log.warn("查询会员卡列表参数为空");
            return new MembershipCardQueryResponse();
        }

        MembershipCardQueryResponse response = new MembershipCardQueryResponse();
        
        try {
            List<Long> userNos = Collections.singletonList(Long.parseLong(request.getUserNo()));
            
            // 查询飞享会员卡列表
            List<MembershipCardDto> renewCardList = queryRenewCardList(userNos);
            response.setRenewCardList(renewCardList);
            
            // 查询飞跃会员卡列表
            List<MembershipCardDto> vipOrderList = queryVipOrderList(userNos);
            response.setVipOrderList(vipOrderList);
            
        } catch (Exception e) {
            log.error("查询会员卡列表失败, userNo: {}", request.getUserNo(), e);
        }
        
        return response;
    }

    /**
     * 查询飞享会员卡列表
     */
    private List<MembershipCardDto> queryRenewCardList(List<Long> userNos) {
        try {
            List<RenewLogAdminDto> renewCardListResponse = vipFacadeClient.renewLogAdmin(userNos, null, null);
            
            if (CollectionUtils.isEmpty(renewCardListResponse)) {
                return Collections.emptyList();
            }
            
            return convertRenewCardList(renewCardListResponse);
        } catch (Exception e) {
            log.error("查询飞享会员卡列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询飞跃会员卡列表
     */
    private List<MembershipCardDto> queryVipOrderList(List<Long> userNos) {
        try {
            List<VipOrderDetailAdminDTO> vipOrderDetailDTOS = vipFacadeClient.queryVipOrderList(userNos, null, null, null);
            
            if (CollectionUtils.isEmpty(vipOrderDetailDTOS)) {
                return Collections.emptyList();
            }
            
            return convertVipOrderList(vipOrderDetailDTOS);
        } catch (Exception e) {
            log.error("查询飞跃会员卡列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换飞享会员卡数据
     */
    private List<MembershipCardDto> convertRenewCardList(List<RenewLogAdminDto> renewCardList) {
        if (CollectionUtils.isEmpty(renewCardList)) {
            return Collections.emptyList();
        }

        return renewCardList.stream().map(this::convertRenewCard).collect(Collectors.toList());
    }

    /**
     * 转换飞跃会员卡数据
     */
    private List<MembershipCardDto> convertVipOrderList(List<VipOrderDetailAdminDTO> vipOrderList) {
        if (CollectionUtils.isEmpty(vipOrderList)) {
            return Collections.emptyList();
        }

        return vipOrderList.stream().map(this::convertVipOrder).collect(Collectors.toList());
    }

    /**
     * 转换单个飞享会员卡
     */
    private MembershipCardDto convertRenewCard(RenewLogAdminDto renewCard) {
        MembershipCardDto dto = new MembershipCardDto();
        
        // 基本信息
        dto.setCardId(String.valueOf(renewCard.getId()));
        dto.setCardName(renewCard.getVipName());
        dto.setOrderNumber(renewCard.getOrderNumber());
        dto.setCardType(1); // 1-飞享会员卡
        
        // 金额信息
        if (renewCard.getPayPrice() != null) {
            dto.setActualAmount(new BigDecimal(renewCard.getPayPrice()).multiply(new BigDecimal("0.01")));
        }
        
        // 时间信息
        if (renewCard.getStartAt() != null) {
            dto.setValidStartDate(renewCard.getStartAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            dto.setOrderTime(renewCard.getStartAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (renewCard.getEndAt() != null) {
            dto.setValidEndDate(renewCard.getEndAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
        }
        
        // 状态信息
        dto.setStatus(getCardStatus(renewCard.getStatus()));
        
        return dto;
    }

    /**
     * 转换单个飞跃会员卡
     */
    private MembershipCardDto convertVipOrder(VipOrderDetailAdminDTO vipOrder) {
        MembershipCardDto dto = new MembershipCardDto();
        
        // 基本信息
        dto.setCardId(String.valueOf(vipOrder.getId()));
        dto.setCardName(vipOrder.getVipCardName());
        dto.setOrderNumber(vipOrder.getVipOrderNo());
        dto.setCardType(2); // 2-飞跃会员卡
        
        // 金额信息
        if (vipOrder.getPayPrice() != null) {
            dto.setActualAmount(new BigDecimal(vipOrder.getPayPrice()).multiply(new BigDecimal("0.01")));
        }
        
        // 时间信息
        if (vipOrder.getStartTime() != null) {
            dto.setValidStartDate(vipOrder.getStartTime().toLocalDate());
        }
        if (vipOrder.getEndTime() != null) {
            dto.setValidEndDate(vipOrder.getEndTime().toLocalDate());
        }
        if (vipOrder.getCreateTime() != null) {
            dto.setOrderTime(vipOrder.getCreateTime());
        }
        
        // 状态信息
        dto.setStatus(getVipOrderStatus(vipOrder.getOrderStatus()));
        
        return dto;
    }

    /**
     * 获取飞享会员卡状态描述
     */
    private String getCardStatus(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case 1:
                return "生效中";
            case 2:
                return "已结束";
            case 3:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 获取飞跃会员卡状态描述
     */
    private String getVipOrderStatus(String orderStatus) {
        if (StringUtils.isBlank(orderStatus)) {
            return "未知";
        }
        
        switch (orderStatus) {
            case "PAY_SUCCESS":
                return "生效中";
            case "EXPIRED":
                return "已结束";
            case "CANCELLED":
                return "已取消";
            default:
                return "未知";
        }
    }
}
