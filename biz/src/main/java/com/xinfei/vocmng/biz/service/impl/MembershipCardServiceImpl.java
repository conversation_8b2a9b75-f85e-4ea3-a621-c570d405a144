/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.google.common.collect.Lists;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RenewLogAdminDto;
import com.xinfei.vocmng.biz.mapstruct.VipCardConverter;
import com.xinfei.vocmng.biz.model.req.MembershipCardQueryRequest;
import com.xinfei.vocmng.biz.model.resp.MembershipCardDto;
import com.xinfei.vocmng.biz.model.resp.MembershipCardQueryResponse;
import com.xinfei.vocmng.biz.rr.dto.MemberCardDto;
import com.xinfei.vocmng.biz.service.MembershipCardService;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.itl.model.enums.CardTypeEnum;
import com.xinfei.vocmng.itl.model.enums.PayTypeEnum;
import com.xinfei.vocmng.itl.model.enums.PayTypeRenewEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 会员卡服务实现类
 *
 * <AUTHOR>
 * @version $ MembershipCardServiceImpl, v 0.1 2025/8/25 15:00 shaohui.chen Exp $
 */
@Slf4j
@Service
public class MembershipCardServiceImpl implements MembershipCardService {

    @Autowired
    private VipFacadeClientImpl vipFacadeClient;

    @Override
    public MembershipCardQueryResponse queryMembershipCards(MembershipCardQueryRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getUserNo())) {
            log.warn("查询会员卡列表参数为空");
            return new MembershipCardQueryResponse();
        }

        MembershipCardQueryResponse response = new MembershipCardQueryResponse();
        
        try {
            List<Long> userNos = Collections.singletonList(Long.parseLong(request.getUserNo()));
            
            // 查询飞享会员卡列表
            List<MembershipCardDto> renewCardList = queryRenewCardList(userNos);
            response.setRenewCardList(renewCardList);
            
            // 查询飞跃会员卡列表
            List<MembershipCardDto> vipOrderList = queryVipOrderList(userNos);
            response.setVipOrderList(vipOrderList);
            
        } catch (Exception e) {
            log.error("查询会员卡列表失败, userNo: {}", request.getUserNo(), e);
        }
        
        return response;
    }

    /**
     * 查询飞享会员卡列表
     */
    private List<MembershipCardDto> queryRenewCardList(List<Long> userNos) {
        try {
            List<RenewLogAdminDto> renewCardListResponse = vipFacadeClient.renewLogAdmin(userNos, null, null);
            
            if (CollectionUtils.isEmpty(renewCardListResponse)) {
                return Collections.emptyList();
            }
            
            return convertRenewCardList(renewCardListResponse);
        } catch (Exception e) {
            log.error("查询飞享会员卡列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询飞跃会员卡列表
     */
    private List<MembershipCardDto> queryVipOrderList(List<Long> userNos) {
        try {
            List<VipOrderDetailAdminDTO> vipOrderDetailDTOS = vipFacadeClient.queryVipOrderList(userNos, null, null, null);
            
            if (CollectionUtils.isEmpty(vipOrderDetailDTOS)) {
                return Collections.emptyList();
            }
            
            return convertVipOrderList(vipOrderDetailDTOS);
        } catch (Exception e) {
            log.error("查询飞跃会员卡列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换飞享会员卡数据
     */
    private List<MembershipCardDto> convertRenewCardList(List<RenewLogAdminDto> renewCardList) {
        if (CollectionUtils.isEmpty(renewCardList)) {
            return Collections.emptyList();
        }

        List<MembershipCardDto> result = Lists.newArrayListWithCapacity(renewCardList.size());
        for (RenewLogAdminDto card : renewCardList) {
            // 使用现有的转换器转换为MemberCardDto
            MemberCardDto memberCardDto = VipCardConverter.INSTANCE.renewLogAdminDtoToMemberCardDto(card);
            memberCardDto.setType(CardTypeEnum.RENEW_CARD.getType());

            // 当订单类型为飞享会员时，创建时间字段改为取created_time
            if ("飞享会员".equals(memberCardDto.getCardName())) {
                memberCardDto.setCreateTime(card.getCreateTime());
            }
            memberCardDto.setPayTypeDesc(PayTypeRenewEnum.getDescByCode(card.getPayType()));
            memberCardDto.setPayAmount(memberCardDto.getPayAmount().multiply(new BigDecimal("0.01")));

            // 转换为MembershipCardDto
            MembershipCardDto dto = convertToMembershipCardDto(memberCardDto, 1);
            result.add(dto);
        }

        return result;
    }

    /**
     * 转换飞跃会员卡数据
     */
    private List<MembershipCardDto> convertVipOrderList(List<VipOrderDetailAdminDTO> vipOrderList) {
        if (CollectionUtils.isEmpty(vipOrderList)) {
            return Collections.emptyList();
        }

        List<MembershipCardDto> result = Lists.newArrayListWithCapacity(vipOrderList.size());
        for (VipOrderDetailAdminDTO card : vipOrderList) {
            // 使用现有的转换器转换为MemberCardDto
            MemberCardDto memberCardDto = VipCardConverter.INSTANCE.vipOrderDtoToMemberCardDto(card);
            memberCardDto.setType(CardTypeEnum.VIP_CARD.getType());
            memberCardDto.setPayTypeDesc(PayTypeEnum.getDescByCode(memberCardDto.getPayType()));

            // 转换为MembershipCardDto
            MembershipCardDto dto = convertToMembershipCardDto(memberCardDto, 2);
            result.add(dto);
        }

        return result;
    }

    /**
     * 将MemberCardDto转换为MembershipCardDto
     */
    private MembershipCardDto convertToMembershipCardDto(MemberCardDto memberCardDto, Integer cardType) {
        MembershipCardDto dto = new MembershipCardDto();

        // 基本信息
        if (memberCardDto.getCardId() != null) {
            dto.setCardId(String.valueOf(memberCardDto.getCardId()));
        }
        dto.setCardName(memberCardDto.getCardName());
        dto.setOrderNumber(memberCardDto.getOrderNo());
        dto.setCardType(cardType);

        // 金额信息
        dto.setActualAmount(memberCardDto.getPayAmount());

        // 时间信息 - 从字符串转换为LocalDate和LocalDateTime
        if (StringUtils.isNotBlank(memberCardDto.getBeginTime())) {
            try {
                LocalDate startDate = LocalDate.parse(memberCardDto.getBeginTime().substring(0, 10));
                dto.setValidStartDate(startDate);
            } catch (Exception e) {
                log.warn("解析开始时间失败: {}", memberCardDto.getBeginTime(), e);
            }
        }

        if (StringUtils.isNotBlank(memberCardDto.getEndTime())) {
            try {
                LocalDate endDate = LocalDate.parse(memberCardDto.getEndTime().substring(0, 10));
                dto.setValidEndDate(endDate);
            } catch (Exception e) {
                log.warn("解析结束时间失败: {}", memberCardDto.getEndTime(), e);
            }
        }

        if (StringUtils.isNotBlank(memberCardDto.getCreateTime())) {
            try {
                LocalDateTime orderTime = LocalDateTime.parse(memberCardDto.getCreateTime().replace(" ", "T"));
                dto.setOrderTime(orderTime);
            } catch (Exception e) {
                log.warn("解析创建时间失败: {}", memberCardDto.getCreateTime(), e);
            }
        }

        // 状态信息
        dto.setStatus(memberCardDto.getVipStatus());

        return dto;
    }
}
