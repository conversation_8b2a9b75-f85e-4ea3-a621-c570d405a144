/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

/**
 * <AUTHOR>
 * @version $ GlobalExceptionHandler, v 0.1 2023/12/24 19:45 wancheng.qu Exp $
 */

import com.xinfei.vocmng.biz.model.enums.ErrorLevelsEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.util.constant.MonitorConstant;
import com.xinfei.vocmng.util.enums.ErrLevelsEnum;
import com.xinfei.vocmng.util.exception.VocmngException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 设置监控日志结果状态
     * @param result 结果状态
     */
    private void setMonitorLogResult(boolean result) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(attributes)) {
            HttpServletRequest request = attributes.getRequest();
            request.setAttribute(MonitorConstant.MonitorKey.MONITOR_LOG_RESULT, result);
        }
    }

    @ExceptionHandler(TechplayException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleTechplayException(TechplayException ex) {
        // 根据异常等级判断是否设置监控日志结果为false
        if (Objects.nonNull(ex.getResultCodeEnum()) &&
            (ErrorLevelsEnum.ERROR.equals(ex.getResultCodeEnum().getErrorLevel()) ||
             ErrorLevelsEnum.FATAL.equals(ex.getResultCodeEnum().getErrorLevel()))) {
            setMonitorLogResult(false);
        }
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getResultCodeEnum(), ex.getMsg());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(IgnoreException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleIgnoreException(IgnoreException ex) {
        // IgnoreException不修改监控日志状态
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getResultCodeEnum(), ex.getMsg());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(com.xinfei.vocmng.itl.client.exeception.IgnoreException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleItlIgnoreException(com.xinfei.vocmng.itl.client.exeception.IgnoreException ex) {
        // ITL包下的IgnoreException不修改监控日志状态
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getResultCodeEnum(), ex.getMsg());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(VocmngException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleVocmngException(VocmngException ex) {
        // 根据异常等级判断是否设置监控日志结果为false
        if (Objects.nonNull(ex.getResultCodeEnum()) &&
                (ErrLevelsEnum.ERROR.equals(ex.getResultCodeEnum().getErrorLevel()) ||
                        ErrLevelsEnum.FATAL.equals(ex.getResultCodeEnum().getErrorLevel()))) {
            setMonitorLogResult(false);
        }
        ApiResponse<String> responseDTO = ApiResponse.fail(ex);
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(ClientException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleClientException(ClientException ex) {
        // ClientException直接设置监控日志结果为false
        setMonitorLogResult(false);
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getErrDtlEnum(), ex.getMessage());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleValidationExceptions(MethodArgumentNotValidException ex){
        setMonitorLogResult(false);
        List<String> errorMsgList = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach((error)->{
            errorMsgList.add(error.getDefaultMessage());
        });

        ApiResponse<String> response = ApiResponse.paramIllegal(StringUtils.join(errorMsgList));
        return new ResponseEntity<>(response,HttpStatus.OK);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleGenericException(Exception ex) {
        setMonitorLogResult(false);
        log.error("全局异常。", ex);
        ApiResponse<String> responseDTO = ApiResponse.fail("系统异常，请稍后重试");
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }
}
