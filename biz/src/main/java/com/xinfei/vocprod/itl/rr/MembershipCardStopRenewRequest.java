/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 会员卡停止续费请求
 *
 * <AUTHOR>
 * @version $ MembershipCardStopRenewRequest, v 0.1 2025/8/25 16:30 shaohui.chen Exp $
 */
@Data
public class MembershipCardStopRenewRequest {

    @ApiModelProperty(value = "用户编号", required = true)
    @NotBlank(message = "用户编号不能为空")
    private String userNo;

    @ApiModelProperty(value = "会员卡类型：1-飞享会员，2-飞跃会员", required = true)
    @NotNull(message = "会员卡类型不能为空")
    private Integer cardType;

}
