/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员卡退款响应
 *
 * <AUTHOR>
 * @version $ MembershipCardRefundResponse, v 0.1 2025/8/25 16:30 shaohui.chen Exp $
 */
@Data
public class MembershipCardRefundResponse {

    @ApiModelProperty("是否成功")
    private Boolean success;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("退款申请ID")
    private Long refundApplyId;

}
