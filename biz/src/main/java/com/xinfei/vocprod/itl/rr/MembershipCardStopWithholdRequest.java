/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会员卡取消扣款请求
 *
 * <AUTHOR>
 * @version $ MembershipCardStopWithholdRequest, v 0.1 2025/8/25 16:30 shaohui.chen Exp $
 */
@Data
public class MembershipCardStopWithholdRequest {

    @ApiModelProperty(value = "会员卡ID", required = true)
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    @ApiModelProperty(value = "会员卡类型：1-飞享会员，2-飞跃会员", required = true)
    @NotNull(message = "会员卡类型不能为空")
    private Integer cardType;

}
