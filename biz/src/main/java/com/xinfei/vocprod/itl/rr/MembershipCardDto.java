package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会员卡查询请求
 *
 * <AUTHOR>
 * @version $ MembershipCardDto, v 0.1 2025/8/25 14:40 shaohui.chen Exp $
 */
@Data
public class MembershipCardDto {

    @ApiModelProperty("会员卡ID")
    private String cardId;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("会员卡状态：状态、生效中、已结束")
    private String status;

    @ApiModelProperty("实付金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("有效期开始日期")
    private LocalDate validStartDate;

    @ApiModelProperty("有效期结束日期")
    private LocalDate validEndDate;

    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("会员卡类型：1-飞享会员卡，2-飞跃会员卡")
    private Integer cardType;

    @ApiModelProperty("订单号")
    private String orderNumber;
}

