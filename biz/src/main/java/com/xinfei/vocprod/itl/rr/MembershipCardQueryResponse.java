package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 会员卡查询响应
 *
 * <AUTHOR>
 * @version $ MembershipCardQueryResponse, v 0.1 2025/8/25 14:40 shaohui.chen Exp $
 */
@Data
public class MembershipCardQueryResponse {

    @ApiModelProperty(value = "飞享会员卡列表")
    private List<MembershipCardDto> renewCardList;

    @ApiModelProperty(value = "飞跃会员卡列表")
    private List<MembershipCardDto> vipOrderList;

}

