/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 会员卡退款请求
 *
 * <AUTHOR>
 * @version $ MembershipCardRefundRequest, v 0.1 2025/8/25 16:30 shaohui.chen Exp $
 */
@Data
public class MembershipCardRefundRequest {

    @ApiModelProperty(value = "会员卡ID", required = true)
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    @ApiModelProperty(value = "会员卡类型：1-飞享会员，2-飞跃会员", required = true)
    @NotNull(message = "会员卡类型不能为空")
    private Integer cardType;

    @ApiModelProperty(value = "退款金额（元）")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款原因")
    private String refundReason;

}
