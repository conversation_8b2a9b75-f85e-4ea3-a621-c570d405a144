/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.req.MembershipCardQueryRequest;
import com.xinfei.vocmng.biz.model.resp.MembershipCardQueryResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 会员卡服务测试
 *
 * <AUTHOR>
 * @version $ MembershipCardServiceTest, v 0.1 2025/8/25 15:30 shaohui.chen Exp $
 */
public class MembershipCardServiceTest extends TechplayDevTestBase {

    @Autowired
    private MembershipCardService membershipCardService;

    @Test
    public void testQueryMembershipCards() {
        MembershipCardQueryRequest request = new MembershipCardQueryRequest();
        request.setUserNo("123456789"); // 使用测试用户编号
        
        MembershipCardQueryResponse response = membershipCardService.queryMembershipCards(request);
        
        System.out.println("飞享会员卡数量: " + (response.getRenewCardList() != null ? response.getRenewCardList().size() : 0));
        System.out.println("飞跃会员卡数量: " + (response.getVipOrderList() != null ? response.getVipOrderList().size() : 0));
        
        if (response.getRenewCardList() != null && !response.getRenewCardList().isEmpty()) {
            System.out.println("第一张飞享会员卡: " + response.getRenewCardList().get(0));
        }
        
        if (response.getVipOrderList() != null && !response.getVipOrderList().isEmpty()) {
            System.out.println("第一张飞跃会员卡: " + response.getVipOrderList().get(0));
        }
    }
}
