/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocprod.itl.rr.MembershipCardQueryRequest;
import com.xinfei.vocprod.itl.rr.MembershipCardQueryResponse;
import com.xinfei.vocprod.itl.rr.MembershipCardRefundRequest;
import com.xinfei.vocprod.itl.rr.MembershipCardRefundResponse;
import com.xinfei.vocprod.itl.rr.MembershipCardStopRenewRequest;
import com.xinfei.vocprod.itl.rr.MembershipCardStopWithholdRequest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * VocprodApi测试
 *
 * <AUTHOR>
 * @version $ VocprodApiTest, v 0.1 2025/8/25 16:00 shaohui.chen Exp $
 */
public class VocprodApiTest extends TechplayDevTestBase {

    @Autowired
    private VocprodApi vocprodApi;

    @Test
    public void testQueryMembershipCards() {
        MembershipCardQueryRequest request = new MembershipCardQueryRequest();
        request.setUserNo("123456789"); // 使用测试用户编号
        
        MembershipCardQueryResponse response = vocprodApi.queryMembershipCards(request).getData();
        
        System.out.println("查询会员卡列表测试:");
        System.out.println("飞享会员卡数量: " + (response.getRenewCardList() != null ? response.getRenewCardList().size() : 0));
        System.out.println("飞跃会员卡数量: " + (response.getVipOrderList() != null ? response.getVipOrderList().size() : 0));
    }

    @Test
    public void testRenewStopAdmin() {
        MembershipCardStopRenewRequest request = new MembershipCardStopRenewRequest();
        request.setUserNo("123456789");
        request.setCardType(1); // 飞享会员

        try {
            Boolean result = vocprodApi.renewStopAdmin(request).getData();
            System.out.println("停止续费测试结果: " + result);
        } catch (Exception e) {
            System.out.println("停止续费测试异常: " + e.getMessage());
        }
    }

    @Test
    public void testStopWithhold() {
        MembershipCardStopWithholdRequest request = new MembershipCardStopWithholdRequest();
        request.setCardId(123456L);
        request.setCardType(1); // 飞享会员

        try {
            Boolean result = vocprodApi.stopWithhold(request).getData();
            System.out.println("取消扣款测试结果: " + result);
        } catch (Exception e) {
            System.out.println("取消扣款测试异常: " + e.getMessage());
        }
    }

    @Test
    public void testVipCardRefundApply() {
        MembershipCardRefundRequest request = new MembershipCardRefundRequest();
        request.setCardId(123456L);
        request.setCardType(1); // 飞享会员
        request.setRefundAmount(new BigDecimal("100.00"));
        request.setRefundReason("测试退款");

        try {
            MembershipCardRefundResponse result = vocprodApi.vipCardRefundApply(request).getData();
            System.out.println("退款申请测试结果: " + result.getSuccess() + ", 消息: " + result.getMessage());
        } catch (Exception e) {
            System.out.println("退款申请测试异常: " + e.getMessage());
        }
    }
}
