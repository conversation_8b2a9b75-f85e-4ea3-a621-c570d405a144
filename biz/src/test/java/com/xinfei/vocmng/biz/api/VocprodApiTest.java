/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.TechplayDevTestBase;
import com.xinfei.vocmng.biz.model.req.MembershipCardQueryRequest;
import com.xinfei.vocmng.biz.model.resp.MembershipCardQueryResponse;
import com.xinfei.vocmng.biz.rr.request.RenewStopRequest;
import com.xinfei.vocmng.biz.rr.request.StopWithholdRequest;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApply;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;

/**
 * VocprodApi测试
 *
 * <AUTHOR>
 * @version $ VocprodApiTest, v 0.1 2025/8/25 16:00 shaohui.chen Exp $
 */
public class VocprodApiTest extends TechplayDevTestBase {

    @Autowired
    private VocprodApi vocprodApi;

    @Test
    public void testQueryMembershipCards() {
        MembershipCardQueryRequest request = new MembershipCardQueryRequest();
        request.setUserNo("123456789"); // 使用测试用户编号
        
        MembershipCardQueryResponse response = vocprodApi.queryMembershipCards(request).getData();
        
        System.out.println("查询会员卡列表测试:");
        System.out.println("飞享会员卡数量: " + (response.getRenewCardList() != null ? response.getRenewCardList().size() : 0));
        System.out.println("飞跃会员卡数量: " + (response.getVipOrderList() != null ? response.getVipOrderList().size() : 0));
    }

    @Test
    public void testRenewStopAdmin() {
        RenewStopRequest request = new RenewStopRequest();
        request.setUserNo("123456789");
        request.setType(3); // 飞享会员
        request.setVipStatus("支付成功");
        
        try {
            Boolean result = vocprodApi.renewStopAdmin(request).getData();
            System.out.println("停止续费测试结果: " + result);
        } catch (Exception e) {
            System.out.println("停止续费测试异常: " + e.getMessage());
        }
    }

    @Test
    public void testStopWithhold() {
        StopWithholdRequest request = new StopWithholdRequest();
        request.setCardId(123456L);
        request.setType(3); // 飞享会员
        request.setVipStatus("处理中");
        
        try {
            Boolean result = vocprodApi.stopWithhold(request).getData();
            System.out.println("取消扣款测试结果: " + result);
        } catch (Exception e) {
            System.out.println("取消扣款测试异常: " + e.getMessage());
        }
    }

    @Test
    public void testVipCardRefundApply() {
        VipCardRefundApply refundApply = new VipCardRefundApply();
        refundApply.setUserNo("123456789");
        refundApply.setVipCardId(123456L);
        refundApply.setCardType(3); // 飞享会员
        refundApply.setAmount(new BigDecimal("100.00"));
        refundApply.setRefundType(1); // 退款
        refundApply.setReason("测试退款");
        refundApply.setRefundChannel(1); // 原路退回
        vocprodApi.vipCardRefundApply(Collections.singletonList(refundApply)).getData();
    }
}
