# VocprodApi 会员卡相关接口文档（更新版）

## 1. 查询飞享&飞跃会员卡列表

**接口路径**: `/vocmng/queryMembershipCards`  
**请求方式**: POST  
**接口描述**: 查询用户的飞享&飞跃会员卡列表信息

### 请求参数
```json
{
    "userNo": "用户编号"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "renewCardList": [
            {
                "cardId": "会员卡ID",
                "cardName": "会员卡名称",
                "status": "会员卡状态",
                "actualAmount": 实付金额,
                "validStartDate": "有效期开始日期",
                "validEndDate": "有效期结束日期", 
                "orderTime": "下单时间",
                "cardType": 1,
                "orderNumber": "订单号"
            }
        ],
        "vipOrderList": [
            {
                "cardId": "会员卡ID",
                "cardName": "会员卡名称",
                "status": "会员卡状态",
                "actualAmount": 实付金额,
                "validStartDate": "有效期开始日期",
                "validEndDate": "有效期结束日期",
                "orderTime": "下单时间", 
                "cardType": 2,
                "orderNumber": "订单号"
            }
        ]
    }
}
```

## 2. 飞享&飞跃会员停止续费

**接口路径**: `/vocmng/renewStopAdmin`  
**请求方式**: POST  
**接口描述**: 停止飞享或飞跃会员的自动续费

### 请求参数
```json
{
    "userNo": "用户编号",
    "cardType": "会员卡类型：1-飞享会员，2-飞跃会员"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success", 
    "data": true
}
```

## 3. 飞享&飞跃会员取消扣款

**接口路径**: `/vocmng/stopWithhold`  
**请求方式**: POST  
**接口描述**: 取消飞享或飞跃会员的扣款操作

### 请求参数
```json
{
    "cardId": "会员卡ID",
    "cardType": "会员卡类型：1-飞享会员，2-飞跃会员"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": true
}
```

## 4. 飞享&飞跃会员卡退款申请

**接口路径**: `/vocmng/vipCardRefundApply`  
**请求方式**: POST  
**接口描述**: 申请飞享或飞跃会员卡退款

### 请求参数
```json
{
    "cardId": "会员卡ID",
    "cardType": "会员卡类型：1-飞享会员，2-飞跃会员",
    "refundAmount": "退款金额（元）",
    "refundReason": "退款原因"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "success": true,
        "message": "退款申请成功",
        "refundApplyId": 123456
    }
}
```

## 注意事项

1. **重新设计的请求参数**: 移除了不必要的字段如vipStatus，简化了参数结构
2. **避免权限校验**: 所有逻辑都在 `MembershipCardService` 中实现，使用"system"作为操作人
3. **智能路由**: 根据会员卡类型（cardType字段）自动选择调用飞享会员或飞跃会员的相应方法
4. **灵活的退款**: 退款申请支持自定义退款金额和原因，如果不指定则使用可退金额
5. **完整的错误处理**: 包含详细的错误信息和日志记录

## 实现特点

- **飞享会员**（cardType=1）: 调用VipFacadeClient的renewStopAdmin、stopWithhold、vipCardRefundApply方法
- **飞跃会员**（cardType=2）: 调用VipFacadeClient的disableVipRenew、cancelVipDeduct、createVipRefundApply方法
- **统一操作人**: 所有操作都使用"system"作为操作人，无需登录态
- **仿照CrowdRefundServiceImpl**: 退款逻辑完全仿照现有的executeVipRefund方法实现
