# VocprodApi 会员卡相关接口文档

## 1. 查询飞享&飞跃会员卡列表

**接口路径**: `/vocmng/queryMembershipCards`  
**请求方式**: POST  
**接口描述**: 查询用户的飞享&飞跃会员卡列表信息

### 请求参数
```json
{
    "userNo": "用户编号"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "renewCardList": [
            {
                "cardId": "会员卡ID",
                "cardName": "会员卡名称",
                "status": "会员卡状态",
                "actualAmount": 实付金额,
                "validStartDate": "有效期开始日期",
                "validEndDate": "有效期结束日期", 
                "orderTime": "下单时间",
                "cardType": 1,
                "orderNumber": "订单号"
            }
        ],
        "vipOrderList": [
            {
                "cardId": "会员卡ID",
                "cardName": "会员卡名称",
                "status": "会员卡状态",
                "actualAmount": 实付金额,
                "validStartDate": "有效期开始日期",
                "validEndDate": "有效期结束日期",
                "orderTime": "下单时间", 
                "cardType": 2,
                "orderNumber": "订单号"
            }
        ]
    }
}
```

## 2. 飞享&飞跃会员停止续费

**接口路径**: `/vocmng/renewStopAdmin`  
**请求方式**: POST  
**接口描述**: 停止飞享或飞跃会员的自动续费

### 请求参数
```json
{
    "userNo": "用户编号",
    "type": "会员卡类型：3-飞享会员，4-飞跃会员",
    "vipStatus": "飞享/飞跃会员状态"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success", 
    "data": true
}
```

## 3. 飞享&飞跃会员取消扣款

**接口路径**: `/vocmng/stopWithhold`  
**请求方式**: POST  
**接口描述**: 取消飞享或飞跃会员的扣款操作

### 请求参数
```json
{
    "cardId": "会员订单ID",
    "type": "会员卡类型：3-飞享会员，4-飞跃会员",
    "vipStatus": "飞享/飞跃会员状态"
}
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": true
}
```

## 4. 飞享&飞跃会员卡退款申请

**接口路径**: `/vocmng/vipCardRefundApply`  
**请求方式**: POST  
**接口描述**: 申请飞享或飞跃会员卡退款

### 请求参数
```json
[
    {
        "userNo": "用户ID",
        "vipCardId": "会员卡ID",
        "cardType": "会员卡类型：3-飞享会员卡，4-飞跃会员",
        "offlineAccount": "线下还款账号",
        "offlineAccountType": "账号类型：1-银行卡，2-支付宝",
        "amount": "退款金额（元）",
        "refundType": "退卡类型：1-退款，2-退卡&退款",
        "reason": "退款原因",
        "refundChannel": "退款方式：1-原路退回，2-线下退回",
        "offlineAccountBankName": "线下退款银行卡名称"
    }
]
```

### 响应参数
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "success": true,
        "message": "退款成功"
    }
}
```

## 注意事项

1. 所有接口都复用了现有的 `MemberInterestRemoteService` 中的方法
2. 根据会员卡类型（type字段）自动选择调用飞享会员或飞跃会员的相应方法
3. 接口参数验证与原有接口保持一致
4. 错误处理逻辑与原有接口保持一致
